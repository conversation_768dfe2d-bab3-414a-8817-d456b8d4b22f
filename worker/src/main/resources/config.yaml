# Data model for custom workflows. This is uber configuration for different records their attributes and actions supported for customized workflow
templateConfig:
  records:
    - id: invoice
      source: QBO
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnAmount
      # Attributes supported for this record type
      attributes:
        # we could have used list of attribute ids but we are using attribute object so that any field can be overridden for a particular record type
        - id: txnAmount
        - id: customer
        - id: txnPaymentStatus
        - id: term
        - id: location
        - id: txnBalanceAmount
        - id: txnApprovalStatus
        - id: txnSendStatus
        - id: txnUpdateStatus
        - id: txnEmailAvailabilityStatus
        - id: createDays
        - id: txnDays
        - id: txnSendDays
        - id: txnApprovalDays
        - id: txnDueDays
        - id: userId
      # Supported help variables for action parameters
      helpVariables:
        # Help variable DisplayName:ProcessVariableName:DataType
        - Company Name:CompanyName:string
        - Company Email:CompanyEmail:string
        - Customer Name:CustomerName:string
        - Customer Email:CustomerEmail:string
        - Invoice Number:DocNumber:string
        - Total Amount:TxnAmount:double #Defined as attribute as well
        - Balance:TxnBalanceAmount:double #Defined as attribute as well
        - Due Date:TxnDueDate:string #We will get formatted value
        - Invoice Date:TxnDate:string #Attribue defines as days
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              name: Send a customer email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
            - id: createTask
              parameters:
                - name: CloseTask
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention
                - name: NotificationAction
                  fieldValues:
                    - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]
          unsupportedAttributes:
            - txnApprovalStatus
            - txnSendStatus
        - id: approval
          precannedTemplateId: invoiceapproval-multicondition
          actionIdMapper:
            actionId: sendForApproval
            subActionIds:
              - createTask
              - sendCompanyEmail
              - sendWhatsNotify
          actions:
            - id: createTask
              # for invoice approval, to make create task action mandatory, marking required as true
              required: true
              parameters:
                - name: CloseTask
                  # UI renders close task based on close task parameter sent as part of payload,
                  # so marking requiredByUI as false to not send close task param for invoice approval
                  requiredByUI: false
                  fieldValues:
                    - txn_approval_changed
            - id: sendCompanyEmail
              parameters:
                - name: Message
                  fieldValues:
                    - ~invoice.approval.sendCompanyEmail.Message #"Hi,\n\n%Record% [[%Record% Number]] is pending approval. Please approve it at the earliest using this task manager link - https://app.qal.qbo.intuit.com/app/taskmanager.\n\nNote that %record%s that are not approved for more than 30 days will be auto rejected.\n\nThanks,\n[[CompanyName]]"
            - id: sendPushNotification
              parameters:
                - name: Subject
                  fieldValues:
                    - ~invoice.reminder.sendPushNotification.Subject #An Invoice needs your attention
                - name: NotificationAction
                  fieldValues:
                    - qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]
          unsupportedAttributes:
            - txnApprovalStatus
            - createDays
            - txnDays
            - txnDueDays
            - txnSendStatus
            - txnEmailAvailabilityStatus
            - txnPaymentStatus
          defaultAttributes:
            - txnAmount

    - id: bill
      source: QBO
      defaultAttributes:
        - txnAmount
      attributes:
        - id: txnAmount
        - id: vendor
        - id: txnPaymentStatus
        - id: txnUpdateStatus
        - id: location
        - id: term
        - id: createDays
        - id: txnDays
        - id: txnDueDays
        - id: userId
      helpVariables:
        #Help variable DisplayName:ProcessVariableName:DataType
        - Company Name:CompanyName:string
        - Company Email:CompanyEmail:string
        - Vendor Name:VendorName:string
        - Bill Number:DocNumber:string
        - Total Amount:TxnAmount:double #Defined as attribute as well
        - Balance Due:TxnBalanceAmount:double #Defined as attribute as well
        - Due Date:TxnDueDate:string
        - Bill Date:TxnDate:string
        - Due Days:TxnDueDays:integer #Defined as attribute as well
      actionGroups:
        - id: reminder
          actionIds:
            - createTask
            - sendCompanyEmail
          actions:
            - id: sendPushNotification
            - id: sendExternalEmail
              name: Send a vendor email
            - id: createTask
              parameters:
                - name: CloseTask
                  possibleFieldValues:
                    - txn_paid
                    - close_manually
        - id: approval
          actions:
            - id: createTask
              required: true
              handler:
                id: wasCreateTaskAndAddConstraint
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_approval_changed
            - id: sendCompanyEmail
          unsupportedAttributes:
            - term
            - createDays

    - id: estimate
      source: QBO
      defaultAttributes:
        - txnAmount
      attributes:
        - id: txnAmount
        - id: customer
        - id: txnStatus
          fieldValueOptions: GET_ESTIMATE_STATUS
        - id: location
        - id: txnEmailAvailabilityStatus
        - id: txnSendStatus
        - id: txnUpdateStatus
        - id: createDays
        - id: txnDays
        - id: txnSendDays
        - id: txnExpirationDays
      helpVariables:
        - Company Name:CompanyName:string
        - Company Email:CompanyEmail:string
        - Customer Name:CustomerName:string
        - Customer Email:CustomerEmail:string
        - Estimate Number:DocNumber:string
        - Estimate Total:TxnAmount:double #Defined as attribute as well
        - Estimate Date:TxnDate:string
        - Expiration Date:TxnExpirationDate:string
        - Estimate Status:TxnStatus:string
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              name: Send a customer email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
                - name: SendAttachment
                  fieldValues:
                    - "false"
                - name: Message
                  fieldValues:
                    - "Hi [[Customer Name]], \n%Record% [[%Record% Number]] needs your attention. Please take a look at the %record% and contact us if you have any questions. \n\nThanks,\n[[Company Name]]"
            - id: createTask
              parameters:
                - name: CloseTask
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              parameters:
                - name: Subject
                  fieldValues:
                    - An Estimate needs your attention
                - name: NotificationAction
                  fieldValues:
                    - qb001://open/estimate

    - id: statement
      source: QBO
      defaultAttributes:
        - customer
      attributes:
        - id: customer
        - id: statementType
      helpVariables:
        - Company Name:CompanyName:string
        - Company Email:CompanyEmail:string
        - Customer Name:CustomerName:string
        - Customer Email:CustomerEmail:string
        - Statement Date:StatementDate:string
      actionGroups:
        - id: scheduledActions
          actions:
            - id: sendStatement
              required: true
              name: Send a customer email

  #List of all attributes supported by different records
  attributes:
    - id: txnAmount
      name: TxnAmount
      type: double
      configurable: true
      multiSelect: false
      defaultOperator: GTE
      defaultValue: 0
    - id: createDays
      name: TxnCreateDays
      type: days
      configurable: true
      multiSelect: false
      defaultOperator: AF
      defaultValue: DATE_TODAY
      unsupportedOperators:
        - BF
    - id: txnDays
      name: TxnDays
      type: days
      configurable: true
      hidden: false
      multiSelect: false
      defaultOperator: AF
      defaultValue: DATE_TODAY
      unsupportedOperators:
        - BF
    - id: txnSendDays
      name: TxnSendDays
      type: days
      configurable: true
      multiSelect: false
      defaultOperator: AF
      defaultValue: DATE_TODAY
      unsupportedOperators:
        - BF
    - id: txnApprovalDays
      name: TxnApprovalDays
      type: days
      configurable: true
      multiSelect: false
      defaultOperator: AF
      defaultValue: DATE_TODAY
      unsupportedOperators:
        - BF
    - id: txnApprovalStatus
      name: TxnApprovalStatus
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_APPROVAL_STATUS
    - id: term
      name: Term
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_TERM
    - id: class
      name: Class
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_CLASSES
    - id: location
      name: Location
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_DEPARTMENT
    - id: customer
      name: Customer
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_CUSTOMER
      defaultOperator: CONTAINS
    - id: vendor
      name: Vendor
      type: string
      configurable: true
      multiSelect: true
      fieldValueOptions: GET_VENDOR
      defaultOperator: CONTAINS
    - id: txnDueDays
      name: TxnDueDays
      type: days
      configurable: true
      multiSelect: false
      defaultOperator: BF
      defaultValue: -1
    - id: txnEmailAvailabilityStatus
      name: TxnEmailAvailabilityStatus
      fieldValueOptions: GET_CUSTOMER_EMAIL_STATUS
      type: string
      configurable: true
    - id: txnDepositStatus
      name: TxnDepositStatus
      fieldValueOptions: GET_DEPOSIT_STATUS
      type: string
      configurable: true
    - id: txnPaymentStatus
      name: TxnPaymentStatus
      fieldValueOptions: GET_PAYMENT_STATUS
      type: string
      configurable: true
    - id: txnSendStatus
      name: TxnSendStatus
      fieldValueOptions: GET_SEND_STATUS
      type: string
      configurable: true
    - id: txnUpdateStatus
      name: TxnUpdateStatus
      fieldValueOptions: GET_UPDATE_STATUS
      type: string
      configurable: true
    - id: txnBalanceAmount
      name: TxnBalanceAmount
      type: double
      configurable: true
    - id: txnStatus
      name: TxnStatus
      type: string
      configurable: true
      multiSelect: true
    - id: txnExpirationDays
      name: TxnExpirationDays
      type: days
      configurable: true
    - id: userId
      name: intuit_userid
      type: string
      configurable: true
      multiSelect: false
      defaultOperator: CONTAINS
      fieldValueOptions: GET_ADMINS_ID
      hidden: true
    - id: statementType
      name: StatementType
      type: string
      configurable: true
      multiSelect: false
      fieldValueOptions: GET_STATEMENT_TYPES

  # Various data types and the operators supported on them
  dataTypes:
    - type: date
      operators:
        - id: LT
          name: Less Than
        - id: GT
          name: Greater Than
    - type: boolean
      operators:
        - id: is
          name: Equal To
    - type: string
      operators:
        - id: CONTAINS
          name: Within
        - id: NOT_CONTAINS
          name: Not Within
    - type: double
      operators:
        - id: LT
          name: Less Than
        - id: GT
          name: Greater Than
        - id: LTE
          name: Less Than Or Equal To
        - id: GTE
          name: Greater Than Or Equal To
        - id: EQ
          name: Equal To
        - id: BTW
          name: Between
    - type: days
      # type inferred by process engine
      nativeType: integer
      operators:
        - id: BF
          name: Before
        - id: AF
          name: After

  actionGroups:
    - id: reminder
      actionIds:
        - createTask
        - sendExternalEmail
        - sendCompanyEmail
        - sendPushNotification
    - id: approval
      actionIds:
        - createTask
        - sendCompanyEmail
    - id: scheduledActions
      actionIds:
        - sendStatement

  parameters:
    - id: entityType
      name: entityType
      handlerFieldName: RecordType
      fieldType: string
      requiredByHandler: true
      valueType: PROCESS_VARIABLE
    - id: txnId
      name: Id
      handlerFieldName: TxnId
      fieldType: string
      valueType: PROCESS_VARIABLE
      requiredByHandler: true
    - id: syncToken
      name: SyncToken
      handlerFieldName: SyncToken
      fieldType: string
      requiredByHandler: true
      valueType: PROCESS_VARIABLE
    - id: realmId
      name: intuit_realmid
      handlerFieldName: RealmId
      fieldType: string
      valueType: PROCESS_VARIABLE
      requiredByHandler: true
    - id: sendTo
      name: SendTo
      handlerFieldName: To
      configurable: true
      requiredByHandler: true
      requiredByUI: true
      fieldType: string
    - id: cc
      name: CC
      fieldType: string
      configurable: true
      requiredByHandler: true
      requiredByUI: true
    - id: bcc
      name: BCC
      fieldType: string
      configurable: true
      requiredByHandler: true
      requiredByUI: true
    - id: message
      name: Message
      fieldType: string
      configurable: true
      requiredByHandler: true
      helpVariablesRequired: true
      requiredByUI: true
    - id: subject
      name: Subject
      fieldType: string
      configurable: true
      requiredByHandler: true
      helpVariablesRequired: true
      requiredByUI: true
    - id: isEmail
      name: IsEmail
      fieldType: boolean
      requiredByHandler: true
    - id: sendAttachment
      name: SendAttachment
      fieldType: boolean
      requiredByHandler: true
      fieldValues:
        - "false"
    - id: notificationAction
      name: NotificationAction
      fieldType: string
      requiredByHandler: true

  actions:
    - id: sendForApproval
      name: Send For Approval
      parameters:
        - name: Assignee
          configurable: true
          actionByUI: GET_ADMINS_ID
          requiredByHandler: true
          requiredByUI: true
          fieldType: string
    - id: createTask
      name: Create a task
      handler:
        id: wasCreateTask
      parameters:
        - id: txnId
        - id: realmId
        - name: ProjectType
          requiredByHandler: true
          fieldType: string
        - name: TaskType
          requiredByHandler: true
          fieldType: string
        - name: Assignee
          configurable: true
          actionByUI: GET_ADMINS_ID
          requiredByHandler: true
          requiredByUI: true
          fieldType: string
        - name: TaskName
          fieldType: string
          configurable: true
          requiredByUI: true
          requiredByHandler: true
          helpVariablesRequired: true
          fieldValues:
            - "Review %Record% [[%Record% Number]]"
        - name: CloseTask
          fieldType: string
          requiredByUI: true
          requiredByHandler: true
          configurable: true
    - id: sendExternalEmail
      name: Send External Email
      handler:
        id: wasSendNotification
      parameters:
        - id: sendTo
          helpVariables:
            - Customer Email:CustomerEmail:string
        - id: cc
        - id: bcc
        - id: subject
          fieldValues:
            - "%Record% [[%Record% Number]] needs your attention"
        - id: message
          fieldValues:
            - "Hi [[Customer Name]], \n%Record% [[%Record% Number]] needs your attention. Please take a look at the attached %record% and contact us if you have any questions. \n\nThanks,\n[[Company Name]]"
        - id: isEmail
          # Override field value if needed
          fieldValues:
            - "true"
        - id: sendAttachment
          fieldValues:
            - "true"
        - id: txnId
        - id: entityType
        - id: syncToken
    - id: sendCompanyEmail
      name: Send a company email
      handler:
        id: wasSendNotification
      parameters:
        - id: sendTo
          helpVariables:
            - Company Email:CompanyEmail:string
          fieldValues:
            - "[[Company Email]]"
        - id: cc
        - id: bcc
        - id: subject
          fieldValues:
            - "Review %Record% [[%Record% Number]]"
        - id: message
          fieldValues:
            - "Hi, \n%Record% [[%Record% Number]] needs your attention. Please take a look at the %record% and complete any outstanding tasks. \n\nThanks,\n[[Company Name]]"
        - id: isEmail
          fieldValues:
            - "true"
    - id: sendPushNotification
      name: Send a push notification
      handler:
        id: wasSendNotification
      parameters:
        - id: subject
          configurable: false
        - id: notificationAction
        - id: txnId
        - id: message
          configurable: false
          fieldValues:
            - Go to QuickBooks to view it.
        - id: sendTo
        - id: realmId
        - name: IsMobile
          requiredByHandler: true
          fieldType: boolean
          configurable: false
          fieldValues:
            - "true"
    - id: sendStatement
      name: Send Statement
      handler:
        id: wasSendStatement
      parameters:
        - id: sendTo
          fieldValues:
            - "[[Customer Email]]"
          helpVariables:
            - Customer Email:CustomerEmail:string
        - id: cc
        - id: bcc
        - id: subject
          fieldValues:
            - "Statement from [[Company Name]]"
        - id: message
          fieldValues:
            - "Dear [[Customer Name]],\n\nHere's a friendly reminder to take a look at your statement dated [[Statement Date]].\n\nRegards,\n[[Company Name]]"
        - name: startDate
          requiredByHandler: true
          fieldType: string
          configurable: true
        - name: endDate
          requiredByHandler: true
          fieldType: string
          configurable: true
        - name: statementType
          requiredByHandler: true
          fieldType: string
          configurable: true
          requiredByUI: false
        - name: customerIds
          requiredByHandler: true
          fieldType: string
          configurable: true

  handlers:
    - id: wasSendNotification
      handlerDetail:
        taskHandler: appconnect
        handlerId: intuit-workflows/was-send-notification
        actionName: executeWorkflowAction
      duzzitRestHandlerDetail:
        taskHandler: appconnect
        handlerId: /intuit-workflows/api/was-send-notification.json
        actionName: executeDuzzitRestAction
    - id: wasCreateTask
      handlerDetail:
        taskHandler: appconnect
        handlerId: intuit-workflows/taskmanager-create-task
        actionName: executeWorkflowAction
        responseFields:
          - projectId
          - closeTaskRule
    - id: wasCreateTaskAndAddConstraint
      handlerDetail:
        taskHandler: appconnect
        handlerId: intuit-workflows/was-create-task-and-add-constraint
        actionName: executeWorkflowAction
        responseFields:
          - projectId
          - closeTaskRule
          - assigneeId
    - id: wasSendStatement
      handlerDetail:
        taskHandler: appconnect
        handlerId: intuit-workflows/was-send-statement
        actionName: executeWorkflowAction


  #Old Templates
  configTemplates:
    - id: paymentDueReminder
      name: Payment Due Reminder
      record: Invoice
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnPaymentStatus
        - txnDueDays
      attributes:
        - id: txnPaymentStatus
          defaultValue: UNPAID
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 3
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              name: Send a customer email
              selected: true
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_paid
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: invoiceUnsentReminder
      name: Send invoice reminder
      record: Invoice
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - txnDueDays
      attributes:
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 3
          defaultOperator: AF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: billVendorReminder
      name: Bill Vendor reminder
      record: Bill
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnPaymentStatus
        - txnDueDays
      attributes:
        - id: txnPaymentStatus
          defaultValue: UNPAID
          defaultOperator: CONTAINS
        - id: txnDueDays
          defaultValue: 1
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_paid
                    - txn_sent
                    - close_manually

    # New Templates
    - id: estimateUnsentReminder
      name: Send Estimate Reminder
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - createDays
      attributes:
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: createDays
          defaultValue: 3
          defaultOperator: AF
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_sent
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: estimateFollowupReminder #Name to be decided yet
      name: Estimate Follow up reminder
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnSendStatus
        - txnStatus
        - txnExpirationDays
      attributes:
        - id: txnStatus
          defaultValue: pending
          defaultOperator: CONTAINS
        - id: txnSendStatus
          defaultValue: UNSENT
          defaultOperator: CONTAINS
        - id: txnExpirationDays
          defaultValue: 3
          defaultOperator: BF
      actionGroups:
        - id: reminder
          actions:
            - id: sendExternalEmail
              selected: true
              name: Send a customer email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Customer Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_accepted
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention

    - id: openEstimateReminder
      name: Open estimate reminder
      record: Estimate
      # Default attributes. Rule lines are created based on these attributes
      defaultAttributes:
        - txnStatus
      attributes:
        - id: txnStatus
          defaultValue: pending,accepted
          defaultOperator: CONTAINS
      actionGroups:
        - id: reminder
          actions:
            - id: sendCompanyEmail
              selected: true
              name: Send a company email
              parameters:
                - name: SendTo
                  fieldValues:
                    - "[[Company Email]]"
            - id: createTask
              selected: true
              parameters:
                - name: CloseTask
                  fieldValues:
                    - txn_accepted
                  possibleFieldValues:
                    - txn_accepted
                    - txn_sent
                    - close_manually
            - id: sendPushNotification
              selected: true
              parameters:
                - name: Subject
                  fieldValues:
                    - An Invoice needs your attention
