package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.authz.WASAuthZClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AdminAPIConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthorizationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Permission;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.WasAuthorizeRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.identity.authz.sdk.client.AuthZClient;
import com.intuit.identity.authz.sdk.client.AuthZErrorCode;
import com.intuit.identity.authz.sdk.exception.AuthZException;
import com.intuit.identity.authz.sdk.model.AuthZBatchRequest;
import com.intuit.identity.authz.sdk.model.AuthZBatchResponse;
import com.intuit.identity.authz.sdk.model.AuthZDecision;
import com.intuit.identity.authz.sdk.model.AuthZRequest;
import com.intuit.identity.authz.sdk.model.AuthZResponse;
import com.intuit.identity.authz.sdk.model.Obligation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * <AUTHOR>
 */
public class AccessVerifierTest {

  @InjectMocks private AccessVerifier accessVerifier;

  @Mock private WASAuthZClient wasAuthZClient;

  @Mock private AuthZClient authZClient;

  @Mock private WASContextHandler contextHandler;

  @Mock private AuthorizationConfig authorizationConfig;

  @Mock private AppConfig appConfig;

  @Mock private AdminAPIConfig adminAPIConfig;

  @Mock private DefinitionRbacConfig definitionRbacConfig;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(appConfig.getAppId()).thenReturn("appid");
    Mockito.when(appConfig.getAppSecret()).thenReturn("secretId");
    Mockito.when(authorizationConfig.getEnv()).thenReturn("QAL");
    Mockito.when(authorizationConfig.getConnectionTimeOutMs()).thenReturn(1000);
    Mockito.when(wasAuthZClient.getAuthZClient()).thenReturn(authZClient);
  }

  @Test
  @SneakyThrows
  public void testPermit() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.PERMIT);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertTrue(
        accessVerifier.verifyAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }

  @Test
  @SneakyThrows
  public void testPermitWithOnBehalfOf() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.PERMIT);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertTrue(
        accessVerifier.verifyAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .onBehalfOf("1243")
                .build()));
  }

  @Test
  @SneakyThrows
  public void testPermitWithOnBehalfOfAndAssignee() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.PERMIT);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertTrue(
        accessVerifier.verifyAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .onBehalfOf("1243")
                .assignee("124")
                .build()));
  }

  @Test
  @SneakyThrows
  public void testDeny() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.DENY);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertFalse(
        accessVerifier.verifyAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }

  @Test(expected = WorkflowGeneralException.class)
  @SneakyThrows
  public void exceptionUseCase() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"Vdfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
        .thenThrow(AuthZException.class);
    Assert.assertFalse(
        accessVerifier.verifyAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }
  
  
  @Test
  @SneakyThrows
  public void testHumanTaskPermit() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.PERMIT);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertTrue(
        accessVerifier.verifyHumanTaskAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }
  
  
  @Test
  @SneakyThrows
  public void testHumanTaskQBLivePermit() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZResponse response = new AuthZResponse();
    response.setDecision(AuthZDecision.PERMIT);

    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class))).thenReturn(response);
    Assert.assertTrue(
        accessVerifier.verifyHumanTaskAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .taskOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }
  
  @Test(expected = WorkflowGeneralException.class)
  @SneakyThrows
  public void exception_HumanTask_unauthorize() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"Vdfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZException authzException = Mockito.mock(AuthZException.class);
    Mockito.when(authzException.getAuthZErrorCode())
    	.thenReturn(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS);
    
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
        .thenThrow(authzException);
    
    Assert.assertFalse(
        accessVerifier.verifyHumanTaskAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }
  
  @Test(expected = WorkflowGeneralException.class)
  @SneakyThrows
  public void exceptionUseCase_HumanTask_OtherException() {
    String header =
        "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"Vdfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
    Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

    AuthZException authzException = Mockito.mock(AuthZException.class);
    Mockito.when(authzException.getAuthZErrorCode())
    	.thenReturn(AuthZErrorCode.AUTHZ_CIRCUIT_OPEN);
    
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
        .thenThrow(authzException);
    Assert.assertFalse(
        accessVerifier.verifyHumanTaskAccess(
            WasAuthorizeRequest.builder()
                .workflowOwnerId(134l)
                .permission(Permission.TASK_READ)
                .build()));
  }
  
  
	@Test
	@SneakyThrows
	public void testBatchHumanTaskQBLivePermit() {
		String header = "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
		Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

		Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

		List<AuthZResponse> authZResponses = new ArrayList<>();
		AuthZResponse response1 = new AuthZResponse();
		response1.setDecision(AuthZDecision.DENY);
		authZResponses.add(response1);

		AuthZResponse response2 = new AuthZResponse();
		response2.setDecision(AuthZDecision.PERMIT);
		authZResponses.add(response2);

		AuthZResponse response3 = new AuthZResponse();
		response3.setDecision(AuthZDecision.DENY);
		response3.setObligations(new ArrayList<>());
		Obligation obligation = new Obligation();
		obligation.put("Privilege", "Insufficient Privillege");
		response3.getObligations().add(obligation);
		authZResponses.add(response3);

		AuthZBatchResponse response = new AuthZBatchResponse();
		response.setResponses(authZResponses);

		Mockito.when(authZClient.authorize(Mockito.any(AuthZBatchRequest.class))).thenReturn(response);

		List<WasAuthorizeRequest> authorizeRequests = new ArrayList<>();

		authorizeRequests
				.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ).workflowOwnerId(9130357037878906l)
						.domain("QB_LIVE").taskType(TaskType.HUMAN_TASK.name()).taskId("1").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357308501826l).taskOwnerId(Long.valueOf(9130357308501826l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("2").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357037878906l).taskOwnerId(Long.valueOf(9130357037878906l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("3").build());

		Map<String, Boolean> responseMap = accessVerifier.verifyBatchHumanTaskAccess(authorizeRequests);
		Assert.assertFalse(responseMap.get("1"));
		Assert.assertTrue(responseMap.get("2"));
		Assert.assertFalse(responseMap.get("3"));
	}

	@Test(expected = WorkflowGeneralException.class)
	@SneakyThrows
	public void exception_HumanTask_batchAuthZRequest() {
		String header = "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"Vdfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
		Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

		Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

		AuthZException authzException = Mockito.mock(AuthZException.class);
		Mockito.when(authzException.getAuthZErrorCode()).thenReturn(AuthZErrorCode.AUTHZ_CIRCUIT_OPEN);

		Mockito.when(authZClient.authorize(Mockito.any(AuthZBatchRequest.class))).thenThrow(authzException);

		List<WasAuthorizeRequest> authorizeRequests = new ArrayList<>();

		authorizeRequests
				.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ).workflowOwnerId(9130357037878906l)
						.domain("QB_LIVE").taskType(TaskType.HUMAN_TASK.name()).taskId("1").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357308501826l).taskOwnerId(Long.valueOf(9130357308501826l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("2").build());

		accessVerifier.verifyBatchHumanTaskAccess(authorizeRequests);
	}

	@Test(expected = WorkflowGeneralException.class)
	@SneakyThrows
	public void exception_BatchHumanTask_unauthorize() {
		String header = "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"Vdfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
		Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

		Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

		AuthZException authzException = Mockito.mock(AuthZException.class);
		Mockito.when(authzException.getAuthZErrorCode()).thenReturn(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS);

		Mockito.when(authZClient.authorize(Mockito.any(AuthZBatchRequest.class))).thenThrow(authzException);

		List<WasAuthorizeRequest> authorizeRequests = new ArrayList<>();

		authorizeRequests
				.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ).workflowOwnerId(9130357037878906l)
						.domain("QB_LIVE").taskType(TaskType.HUMAN_TASK.name()).taskId("1").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357308501826l).taskOwnerId(Long.valueOf(9130357308501826l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("2").build());

		accessVerifier.verifyBatchHumanTaskAccess(authorizeRequests);
	}

	@Test
	@SneakyThrows
	public void testBatchAuthZ_HandleResponse() {
		String header = "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";
		Mockito.when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn(header);

		Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

		Mockito.when(authZClient.authorize(Mockito.any(AuthZBatchRequest.class))).thenReturn(null);

		List<WasAuthorizeRequest> authorizeRequests = new ArrayList<>();

		authorizeRequests
				.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ).workflowOwnerId(9130357037878906l)
						.domain("QB_LIVE").taskType(TaskType.HUMAN_TASK.name()).taskId("1").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357308501826l).taskOwnerId(Long.valueOf(9130357308501826l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("2").build());

		authorizeRequests.add(WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
				.workflowOwnerId(9130357037878906l).taskOwnerId(Long.valueOf(9130357037878906l)).domain("QB_LIVE")
				.taskType(TaskType.HUMAN_TASK.name()).taskId("3").build());

		Map<String, Boolean> responseMap = accessVerifier.verifyBatchHumanTaskAccess(authorizeRequests);
		Assert.assertFalse(responseMap.get("1"));
		Assert.assertFalse(responseMap.get("2"));
		Assert.assertFalse(responseMap.get("3"));
	}

    @Test
    public void testHasAdminAPIAccess_DevPortalAuthorizationDisabled() {
        when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(false);

        boolean result = accessVerifier.hasAdminAPIAccess();

        assertTrue(result);
        verify(adminAPIConfig, times(1)).isDevPortalAuthorizationEnabled();
    }

    @Test
    public void testHasAdminAPIAccess_Permit() throws AuthZException {
        when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(true);
        when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn("header");
        when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("userId");
        when(contextHandler.get(WASContextEnums.INTUIT_REALMID)).thenReturn("realmId");
        when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        AuthZResponse response = new AuthZResponse();
        response.setDecision(AuthZDecision.PERMIT);

        when(authZClient.authorize(any(AuthZRequest.class))).thenReturn(response);

        boolean result = accessVerifier.hasAdminAPIAccess();

        assertTrue(result);
        verify(authZClient, times(1)).authorize(any(AuthZRequest.class));
    }

    @Test
    public void testHasAdminAPIAccess_Deny() throws AuthZException {
        when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(true);
        when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn("header");
        when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("userId");
        when(contextHandler.get(WASContextEnums.INTUIT_REALMID)).thenReturn("realmId");
        when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        AuthZResponse response = new AuthZResponse();
        response.setDecision(AuthZDecision.DENY);

        when(authZClient.authorize(any(AuthZRequest.class))).thenReturn(response);

        try{
            boolean result = accessVerifier.hasAdminAPIAccess();
        }catch (WorkflowGeneralException e){
            assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, e.getWorkflowError());
        }
    }

    @Test
    public void testHasAdminAPIAccess_Exception() throws AuthZException {
        when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(true);
        when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn("header");
        when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("userId");
        when(contextHandler.get(WASContextEnums.INTUIT_REALMID)).thenReturn("realmId");
        when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        AuthZResponse response = new AuthZResponse();
        response.setDecision(AuthZDecision.DENY);

        when(authZClient.authorize(any(AuthZRequest.class))).thenThrow(new AuthZException(AuthZErrorCode.AUTHZ_CIRCUIT_BREAKER_ERROR));

        try{
            accessVerifier.hasAdminAPIAccess();
        }catch (WorkflowGeneralException e){
            assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, e.getWorkflowError());
        }
    }

    @Test
    public void verifyUserAccessTest() throws AuthZException {
      when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(true);
      when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn("header");
      when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("userId");
      when(contextHandler.get(WASContextEnums.INTUIT_REALMID)).thenReturn("realmId");
      when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
      when(definitionRbacConfig.isEnabledForOperation(CrudOperation.DELETE.name())).thenReturn(true);
      AuthZResponse response = new AuthZResponse();
      response.setDecision(AuthZDecision.PERMIT);

      when(authZClient.authorize(any(AuthZRequest.class))).thenReturn(response);
      assertTrue(accessVerifier.verifyUserAccess("reminder", CrudOperation.DELETE.name()));

      response.setDecision(AuthZDecision.DENY);
      assertFalse(accessVerifier.verifyUserAccess("approval", CrudOperation.DELETE.name()));

    }

    @Test
    public void testHasAdminAPIAccess_AuthZException() throws AuthZException {
        when(adminAPIConfig.isDevPortalAuthorizationEnabled()).thenReturn(true);
        when(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER)).thenReturn("header");
        when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("userId");
        when(contextHandler.get(WASContextEnums.INTUIT_REALMID)).thenReturn("realmId");
        when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");

        when(authZClient.authorize(any(AuthZRequest.class))).thenThrow(new AuthZException("test", AuthZErrorCode.AUTHZ_CIRCUIT_BREAKER_ERROR));

        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, () -> {
            accessVerifier.hasAdminAPIAccess();
        });
        assertNotNull(exception);
    }
  
}
