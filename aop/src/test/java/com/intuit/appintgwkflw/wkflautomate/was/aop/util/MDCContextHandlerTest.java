package com.intuit.appintgwkflw.wkflautomate.was.aop.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

import java.util.Collections;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class MDCContextHandlerTest {

    private MDCContextHandler mdcContextHandler;

    @Before
    public void setUp() {
        mdcContextHandler = new MDCContextHandler();
    }

    @Test
    public void testAddKey() {
        WASContextEnums key = WASContextEnums.AUTHORIZATION_HEADER;
        String value = "Bearer token";

        mdcContextHandler.addKey(key, value);

        assertEquals(value, MDC.get(key.getValue()));
    }

    @Test
    public void testLogTime() {
        String methodName = "testMethod";
        long duration = 123L;

        // Since logTime uses SLF4J's log, we can't directly assert the log output.
        // However, we can ensure no exceptions are thrown and the method executes.
        mdcContextHandler.logTime(methodName, duration);
    }

    @Test
    public void testGet() {
        WASContextEnums key = WASContextEnums.INTUIT_TID;
        String expectedValue = "12345";
        MDC.put(key.getValue(), expectedValue);

        String actualValue = mdcContextHandler.get(key);

        assertEquals(expectedValue, actualValue);
    }

    @Test
    public void testGetAll() {
        Map<String, String> contextMap = Collections.singletonMap("key", "value");
        MDC.setContextMap(contextMap);

        Map<String, String> result = mdcContextHandler.getAll();

        assertEquals(contextMap, result);
    }

    @Test
    public void testGetAll_MDCIsNull() {

        Map<String, String> result = mdcContextHandler.getAll();
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testClear() {
        MDC.put("key", "value");
        mdcContextHandler.clear();
        assertNull(MDC.get("key"));
    }
}