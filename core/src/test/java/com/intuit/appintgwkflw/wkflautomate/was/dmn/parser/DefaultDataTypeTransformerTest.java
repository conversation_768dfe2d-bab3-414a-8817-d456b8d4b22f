package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@Import(WorkflowGlobalConfiguration.class)
public class DefaultDataTypeTransformerTest {

  @Autowired
  private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private DefaultDataTypeTransformer defaultDataTypeTransformer;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    defaultDataTypeTransformer = new DefaultDataTypeTransformer(workflowGlobalConfiguration);
  }

  @Test
  public void testTransformToDmnFriendlyExpression() {
    String dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("EQ 100",
        "TxnAmount",
        "double", true);
    Assert.assertEquals("100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("GTE 100",
        "TxnAmount",
        "double", true);
    Assert.assertEquals(">= 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("LTE 100",
        "TxnAmount",
        "double", true);
    Assert.assertEquals("<= 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("BTW 100,200",
        "TxnAmount",
        "double", true);
    Assert.assertEquals("[100..200]", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("BTW 200,100",
        "TxnAmount",
        "double", true);
    Assert.assertEquals("[100..200]", dmnFriendlyExpr);
  }

  @Test
  public void testTransformToUserFriendlyExpression() {
    String dmnFriendlyExpr = defaultDataTypeTransformer.transformToUserFriendlyExpression(
        "100",
        "TxnAmount");
    Assert.assertEquals("EQ 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToUserFriendlyExpression(
        "TxnAmount <= 100",
        "TxnAmount");
    Assert.assertEquals("LTE 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToUserFriendlyExpression(
        "[100..200]",
        "TxnAmount");
    Assert.assertEquals("GTE 100 && LTE 200", dmnFriendlyExpr);
  }

  @Test
  public void testTransformToLegacyDmnFriendlyExpression() {
    String dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("EQ 100",
        "TxnAmount",
        "double", false);
    Assert.assertEquals("TxnAmount == 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToDmnFriendlyExpression("BTW 100,200",
        "TxnAmount",
        "double", false);
    Assert.assertEquals("TxnAmount >= 100 && TxnAmount <= 200", dmnFriendlyExpr);
  }

  @Test
  public void testTransformToLegacyUserFriendlyExpression() {
    String dmnFriendlyExpr = defaultDataTypeTransformer.transformToUserFriendlyExpression(
        "TxnAmount == 100",
        "TxnAmount");
    Assert.assertEquals("EQ 100", dmnFriendlyExpr);

    dmnFriendlyExpr = defaultDataTypeTransformer.transformToUserFriendlyExpression(
        "TxnAmount >= 100 && TxnAmount <= 200",
        "TxnAmount");
    Assert.assertEquals("GTE 100 && LTE 200", dmnFriendlyExpr);
  }

}
