package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.Variable;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Test;

public class CamundaServiceHelperTest {

  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";

  @Test
  public void testCreateDeployDefinitionRequest() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.readModelFromStream(
            CamundaServiceHelper.class.getClassLoader().getResourceAsStream(INVOICE_APPROVAL_BPMN));
    DmnModelInstance dmnModelInstance =
        Dmn.readModelFromStream(
            CamundaServiceHelper.class.getClassLoader().getResourceAsStream(INVOICE_APPROVAL_DMN));
    List<DmnModelInstance> dmnModelInstanceList = Collections.singletonList(dmnModelInstance);
    DeployDefinition deployDefinition =
        CamundaServiceHelper.createDeployDefinitionRequest(bpmnModelInstance, dmnModelInstanceList,
        		"templateName_ownerId_version");
    Assert.assertNotNull(deployDefinition);
    Assert.assertNotNull(deployDefinition.getBpmnDefinitionFile());
    Assert.assertEquals(1, deployDefinition.getDmnDefinitionFileList().size());
    Assert.assertEquals(DeployDefinitionResponse.class, deployDefinition.getResponseType());
  }

  @Test
  public void prepare_ProcessInstanceUpdateRequest() {
    Map<String, Object> response = new HashMap<>();
    response.put("txnId", "abc");
    CamundaUpdateRequest request = CamundaServiceHelper
        .prepareProcessUpdateRequest("pid1", response);
    Assert.assertEquals("pid1", request.getProcessInstanceId());
    Assert.assertFalse(request.getModifications().isEmpty());
    Assert.assertNotNull(request.getModifications().get("txnId"));
    Assert.assertEquals((Object) "abc", request.getModifications().get("txnId").getValue());
  }
  
  @Test
  public void test_prepareModificationMap_empty() {
	  Map<String, Variable> variableMap = CamundaServiceHelper.prepareModificationMap(new HashMap<>());
	  Assert.assertEquals(0,variableMap.size());
  }
  
  @Test
  public void test_prepareModificationMap() {
	  Map<String, Object> variable = new HashMap<>();
	  Map<String, Object> variableInput1 = new HashMap<>();
	  variableInput1.put(ActivityConstants.VARIABLE_MAP_TYPE, "string");
	  variableInput1.put(ActivityConstants.VARIABLE_MAP_VALUE, "test1");
	  variable.put("abc", variableInput1);
	  
	  Map<String, Object> variableInput2 = new HashMap<>();
	  variableInput2.put(ActivityConstants.VARIABLE_MAP_VALUE, "test2");
	  variable.put("def", variableInput2);
	  
	  Map<String, Object> variableInput3 = new HashMap<>();
	  variableInput3.put(ActivityConstants.VARIABLE_MAP_VALUE, 3l);
	  variable.put("ghi", variableInput3);
	  
	  Map<String, Variable> variableMap = CamundaServiceHelper
			  .prepareModificationMap(variable);
	  
	  Assert.assertEquals(3,variableMap.size());
	  Assert.assertEquals(3l,variableMap.get("ghi").getValue());
	  Assert.assertNull(variableMap.get("ghi").getType());
	  Assert.assertEquals("test2",variableMap.get("def").getValue());
	  Assert.assertNull(variableMap.get("def").getType());
	  Assert.assertEquals("test1",variableMap.get("abc").getValue());
	  Assert.assertEquals("string",variableMap.get("abc").getType());
  }

}