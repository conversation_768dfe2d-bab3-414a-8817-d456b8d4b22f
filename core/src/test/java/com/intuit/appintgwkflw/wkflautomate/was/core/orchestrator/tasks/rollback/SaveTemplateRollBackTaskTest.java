package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class SaveTemplateRollBackTaskTest {

  private SaveTemplateRollBackTask task;

  @Test
  public void testExecute() {

    TemplateDetailsRepository templateDetailsRepository =
        Mockito.mock(TemplateDetailsRepository.class);
    TriggerDetailsRepository triggerDetailsRepository =
        Mockito.mock(TriggerDetailsRepository.class);

    task = new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository);

    State state = new State();
    TemplateDetails template = new TemplateDetails();
    template.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);
    state = task.execute(state);
    Assert.assertNotNull(state);
  }


  @Test
  public void testExecuteWithoutTemplateDeatils() {

    TemplateDetailsRepository templateDetailsRepository =
        Mockito.mock(TemplateDetailsRepository.class);
    TriggerDetailsRepository triggerDetailsRepository =
        Mockito.mock(TriggerDetailsRepository.class);

    task = new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository);
    State state = new State();
    state = task.execute(state);
    Assert.assertNotNull(state);
    verify(templateDetailsRepository, never()).deleteById(Mockito.anyString());
    verify(triggerDetailsRepository, never()).deleteByTemplateDetails(Mockito.any());
  }

  @Test
  public void testExecuteWithoutTemplateId() {

    TemplateDetailsRepository templateDetailsRepository =
        Mockito.mock(TemplateDetailsRepository.class);
    TriggerDetailsRepository triggerDetailsRepository =
        Mockito.mock(TriggerDetailsRepository.class);

    task = new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository);
    State state = new State();

    TemplateDetails template = new TemplateDetails();
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);
    state = task.execute(state);
    Assert.assertNotNull(state);
    verify(templateDetailsRepository, never()).deleteById(Mockito.anyString());
    verify(triggerDetailsRepository, never()).deleteByTemplateDetails(Mockito.any());
  }

  @Test
  public void testExecuteError() {

    TemplateDetailsRepository templateDetailsRepository =
        Mockito.mock(TemplateDetailsRepository.class);
    TriggerDetailsRepository triggerDetailsRepository =
        Mockito.mock(TriggerDetailsRepository.class);

    task = new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository);
    Mockito.doThrow(new NullPointerException()).when(templateDetailsRepository)
        .deleteById("id");

    State state = new State();
    TemplateDetails template = new TemplateDetails();
    template.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);

    state = task.execute(state);
    Assert.assertNotNull(state);
  }

}
