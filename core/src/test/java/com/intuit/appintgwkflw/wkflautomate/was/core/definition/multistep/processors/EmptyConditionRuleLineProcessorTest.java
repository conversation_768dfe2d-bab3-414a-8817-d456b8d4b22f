package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * author btolani
 */
@RunWith(MockitoJUnitRunner.class)
public class EmptyConditionRuleLineProcessorTest {

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");
  private DefinitionInstance definitionInstance;
  private Definition singleStepMultiConditionDefinition;
  private DmnModelInstance dmnModelInstance;
  private BpmnModelInstance multiConditionBpmnModelInstance;
  private DefinitionId definitionId;
  @Mock
  private FeatureFlagManager featureFlagManager;
  @InjectMocks
  private EmptyConditionRuleLineProcessor emptyConditionRuleLineProcessor;
  @InjectMocks
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  @Before
  @SneakyThrows
  public void init() {
    emptyConditionRuleLineProcessor = new EmptyConditionRuleLineProcessor(
        customWorkflowDecisionHandler,featureFlagManager);
    singleStepMultiConditionDefinition = TestHelper.mockSingleStepMultiConditionDefinitionEntity();
    multiConditionBpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML,
                Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML,
                Charset.defaultCharset()));

    DefinitionId.DefinitionIdBuilder definitionIdBuilder =
        DefinitionId.builder().realmId("123L").uniqueId("uniqueId");

    definitionId = definitionIdBuilder.build();
    definitionInstance = new DefinitionInstance(singleStepMultiConditionDefinition,
        multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());

  }

  @Test
  public void test_createDmnHeaders_Success() {
    Map<String, String> expectedInputColumns = new HashMap<>();
    expectedInputColumns.put("Index", "integer");
    Map<String, String> expectedOutputColumns = new HashMap<>();
    expectedOutputColumns.put("decisionResult", "String");

    DecisionTable decisionTable = emptyConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        singleStepMultiConditionDefinition.getWorkflowSteps().stream().findFirst().get(),
        definitionInstance);
    Map<String, String> actualInputColumns = decisionTable.getInputs().stream().collect(
        Collectors.toMap(input -> input.getLabel(),
            input -> input.getInputExpression().getTypeRef()));
    Map<String, String> actualOutputColumns = decisionTable.getOutputs().stream()
        .collect(Collectors.toMap(output -> output.getLabel(), output -> output.getTypeRef()));

    Assert.assertEquals(expectedInputColumns, actualInputColumns);
    Assert.assertEquals(expectedOutputColumns, actualOutputColumns);
  }

  @Test
  public void testBuildDmn() {
    DecisionTable decisionTable = emptyConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance,
        singleStepMultiConditionDefinition.getWorkflowSteps().stream().findFirst().get(),
        definitionInstance);

    Collection<Rule> rules = decisionTable.getRules();

    Map<String, Map<String, DmnHeader>> attributeToHeaderMap =
        new HashMap<>();

    List<String> activityIds = new LinkedList<>();
    String firstCallActivityId = "activity-1";
    activityIds.add(firstCallActivityId);
    Map<String, String> stepIdToActivityId = new HashMap<>();

    String workflowStepId = singleStepMultiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();

    emptyConditionRuleLineProcessor.buildDmn(definitionInstance,
        RuleLineInstance.builder().currentIndexColumnValue(0).workflowStepId(workflowStepId).
            rules(rules).build(), activityIds, attributeToHeaderMap, stepIdToActivityId,
        dmnModelInstance);

    Map<String, String> expectedActivityIdStepIdMap = new HashMap<>();
    String firstActionStepId = "djQuMTpyZWFsbS1pZDpjMjYxMGJkZmFi:actionStep-1";
    expectedActivityIdStepIdMap.put(firstActionStepId, firstCallActivityId);

    Assert.assertEquals(expectedActivityIdStepIdMap, stepIdToActivityId);
  }

  @Test
  public void testBuildDmnHeadersMap() {
    DecisionTable decisionTable = CustomWorkflowUtil.getDecisionTable(dmnModelInstance);
    Assert.assertEquals(Collections.emptyMap(),
        emptyConditionRuleLineProcessor.buildDmnHeadersMap(decisionTable));
  }
}
