package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.getGlobalId;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.MultiStepBpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Arrays;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepReminderTransformerTest {

  private MultiStepReminderTransformer multiStepReminderTransformer;

  private CustomWorkflowConfig customWorkflowConfig;
  @Mock
  private TemplateActionBuilder templateActionBuilder;
  @Mock
  private MultiStepBpmnProcessorImpl multiStepBpmnProcessorImpl;

  DefinitionDetails definitionDetails;
  Definition transformedDefinition;
  Definition oldDefinition;
  TemplateDetails templateDetails;

  @Before
  public void setUp() throws Exception {
    this.customWorkflowConfig = TestHelper.loadCustomConfig();

    multiStepReminderTransformer = new MultiStepReminderTransformer(customWorkflowConfig,
        templateActionBuilder, multiStepBpmnProcessorImpl);

    String ownerId = "1234";
    transformedDefinition = TestHelper.mockCustomWorkflowDefinitionWithParameterType(
        RecordType.INVOICE.getRecordType());
    transformedDefinition.setId(getGlobalId(DEF_ID));
    transformedDefinition.setTemplate(new Template());

    oldDefinition = TestHelper.mockCustomWorkflowDefinitionWithParameterType(
        RecordType.INVOICE.getRecordType());
    oldDefinition.setId(getGlobalId(DEF_ID));
    oldDefinition.setTemplate(new Template());

    templateDetails = TestHelper.mockTemplateDetailsObject();
    definitionDetails = TestHelper.mockDefinitionDetails(transformedDefinition, templateDetails,
        TestHelper.mockAuthorization(ownerId));
  }

  @Test
  public void isMigrationCase() {
    Assert.assertFalse(multiStepReminderTransformer.isMigrationCase(TransformationDecisionDTO.builder()
        .build()));

    Assert.assertTrue(multiStepReminderTransformer.isMigrationCase(TransformationDecisionDTO.builder().isTemplateDataPassed(true).isActivityDetailsPresent(false)
        .build()));
  }

  @Test
  public void migrate() {
    Action action = new Action();
    InputParameter filterCloseTaskCondition = new InputParameter();
    filterCloseTaskCondition.setParameterName(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS);

    InputParameter isRecurring = new InputParameter();
    isRecurring.setParameterName(WorkflowConstants.IS_RECURRING);
    isRecurring.setFieldValues(Arrays.asList("false"));

    InputParameter recurFrequency = new InputParameter();
    recurFrequency.setParameterName(WorkflowConstants.RECUR_FREQUENCY);

    InputParameter maxScheduleCount = new InputParameter();
    maxScheduleCount.setParameterName(WorkflowConstants.MAX_SCHEDULE_COUNT);
    maxScheduleCount.setFieldValues(Arrays.asList("5"));

    action.setParameters(List.of(filterCloseTaskCondition, isRecurring, recurFrequency, maxScheduleCount));

    Mockito.when(templateActionBuilder.buildTemplateStepAction(any(), any(), any()))
        .thenReturn(action);

    multiStepReminderTransformer.migrate(transformedDefinition, definitionDetails);

    //Testing workflow steps
    Assert.assertEquals(2, transformedDefinition.getWorkflowSteps().size());
    WorkflowStep conditionStep = transformedDefinition.getWorkflowSteps().get(0);
    WorkflowStep compositeStep = transformedDefinition.getWorkflowSteps().get(1);
    Assert.assertTrue(MultiStepUtil.isConditionStep(conditionStep));
    Assert.assertTrue(MultiStepUtil.isCompositeStep(compositeStep));
    Assert.assertTrue(MultiStepUtil.isMultiCondition(transformedDefinition));

    List<RuleLine.Rule> ruleList = oldDefinition.getWorkflowSteps(0).getWorkflowStepCondition()
        .getRuleLines(0).getRules();
    Assert.assertEquals(ruleList.get(0),
        conditionStep.getWorkflowStepCondition().getRuleLines(0).getRules(0));
    Assert.assertEquals(ruleList.get(1),
        compositeStep.getWorkflowStepCondition().getRuleLines(0).getRules(0));

    // Testing Action Parameters
    Action action1 = compositeStep.getActionGroup().getAction();
    Assert.assertEquals(4, action1.getParameters().size());
    Assert.assertEquals(Arrays.asList("0"), action1.getParameters().stream()
        .filter(parameter -> WorkflowConstants.RECUR_FREQUENCY.equalsIgnoreCase(
            parameter.getParameterName())).findFirst().get().getFieldValues());

    Assert.assertEquals(Arrays.asList("false"), action1.getParameters().stream()
        .filter(parameter -> WorkflowConstants.IS_RECURRING.equalsIgnoreCase(
            parameter.getParameterName())).findFirst().get().getFieldValues());

    Assert.assertEquals(Arrays.asList("txn_paid"), action1.getParameters().stream()
        .filter(parameter -> WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS.equalsIgnoreCase(
            parameter.getParameterName())).findFirst().get().getFieldValues());

    Assert.assertEquals(Arrays.asList("5"), action1.getParameters().stream()
        .filter(parameter -> WorkflowConstants.MAX_SCHEDULE_COUNT.equalsIgnoreCase(
            parameter.getParameterName())).findFirst().get().getFieldValues());

    Mockito.verify(multiStepBpmnProcessorImpl, Mockito.times(1))
        .setTemplateDataForDefinition(any());

  }
}