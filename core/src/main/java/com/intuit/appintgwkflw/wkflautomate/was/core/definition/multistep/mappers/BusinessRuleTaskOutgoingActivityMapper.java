package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * This class involves the functions to handle the activityIds for multi-condition workflow
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class BusinessRuleTaskOutgoingActivityMapper implements OutgoingActivityMapper {

  @Override
  public List<Pair<String, String>> fetchOutgoingActivityIds(String businessRuleActivityId,
      DefinitionInstance definitionInstance) {
    List<Pair<String, String>> activityIdToCalledElements = new ArrayList<>();

    try {
      BusinessRuleTask businessRuleTask = definitionInstance.getBpmnModelInstance()
          .getModelElementById(businessRuleActivityId);

      // Apart from call activity, there can be other elements in the list of outgoing flow elements of dmn,
      // for example: the default false path in the case of auto approval.
      // But in order to fetch only the outgoing call Activities we need to add a filter condition
      businessRuleTask.getOutgoing().stream()
          .filter(flowElement -> flowElement.getTarget() instanceof CallActivity)
          .forEach(flowElement -> {

        // callActivityExpression = ${decisionResult == '{activityId}'}
        String callActivityExpression = flowElement.getConditionExpression().getTextContent()
            .trim();

        String targetId = flowElement.getTarget().getId();

        // targetExpression -> ${decisionResult == '{targetId}'}
        String targetExpression = String.format(
            WorkflowConstants.DECISION_RESULT_SEQUENCE_FLOW_EXPRESSION, targetId);

        // Remove all spaces from the callActivityExpression and targetExpression
        // For ex: "${decisionResult == 'sendForApproval'}" becomes "${decisionResult=='sendForApproval'}"
        callActivityExpression = StringUtils.deleteWhitespace(callActivityExpression);
        targetExpression = StringUtils.deleteWhitespace(targetExpression);

        // Apart from call activity, there can be other elements in the list of outgoing flow elements of dmn,
        // for example: the default false path in the case of auto approval, so as to filter
        // only the call Activities, we have put this check
        if (callActivityExpression.equals(targetExpression) &&
            (flowElement.getTarget() instanceof CallActivity)) {
          activityIdToCalledElements.add(new Pair<>(
              targetId,
              ((CallActivity) flowElement.getTarget()).getCalledElement()));
        }
      });
    } catch (final Exception exception) {
      WorkflowLogger.logError("step=fetchOutgoingActivityIdsForBusinessRuleActivity " +
          "businessRuleActivityId=%s", businessRuleActivityId);
      throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_MODEL_INSTANCE, exception);
    }

    return activityIdToCalledElements;
  }

}
