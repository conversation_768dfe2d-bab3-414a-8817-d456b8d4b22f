package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectExecuteDuzzitRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;

/**
 *
 *
 * <pre>
 * 1. Retrieve all enabled approval workflows for given realmId
 * 2. validate against list of definitionIds to be deleted
 * 3. get recordType associated with each definition
 * 4. invoke connector to batch disable qbo entity prefs
 * </pre>
 */
@Component
@AllArgsConstructor
public class BulkUpdateApprovalPrefsHandler extends WorkflowTaskHandler {

  private final AuthDetailsService authDetailsService;
  private final AppConnectConfig appConnectConfig;
  private final AppconnectDuzzitRestExecutionHelper appconnectDuzzitRestExecutionHelper;
  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final String DISABLE_SETTINGS =
      "/intuit-workflows/api/disable-settings.json";

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.BULK_UPDATE_APPROVAL_PREFS_HANDLER;
  }

  /**
   * filter approval definition and retrieve record types for which entity prefs need to be disabled
   *
   * @param inputRequest worker request
   * @return
   * @param <T>
   */
  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String activityId = workerActionRequest.getActivityId();
    final String ownerId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    String entityTypesToUpdatePrefs = StringUtils.EMPTY;

      final List<DefinitionPendingDeletion> definitionListPendingDeletion =
        WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest);
    Boolean disableSalesSettings = Boolean.FALSE;
    if (WorkflowTaskUtil.isWorkflowFilterPresent(workerActionRequest)) {

      WorkflowLogger.logInfo(
          "Evaluating workflowFilter %s",
          workerActionRequest.getVariableMap()
              .get(WorkflowConstants.WORKFLOW_FILTER)
      );
      disableSalesSettings = Boolean.TRUE;
    }
    if (!ObjectUtils.isEmpty(definitionListPendingDeletion)){
      /** get all the approval workflows enabled for the given ownerId */
      final Optional<List<DefinitionDetails>> approvalDefinitionList =
          definitionDetailsRepository.findAllEnabledDefinitionsForOwnerIdAndTemplateName(
              Long.parseLong(ownerId),
              ModelType.BPMN,
              CustomWorkflowType.APPROVAL.getTemplateName(),
              false);
      if (!(approvalDefinitionList.isEmpty() || approvalDefinitionList.get().isEmpty())) {
        Set<String> definitionKeys =
            WorkflowVariabilityUtil.getDistinctDefinitionKeys(definitionListPendingDeletion);

        List<DefinitionDetails> approvalDefinitionsToBeDeleted =
            approvalDefinitionList.get().stream()
                .filter(
                    approvalDefinition ->
                        definitionKeys.contains(approvalDefinition.getDefinitionKey()))
                .collect(Collectors.toList());

        if (!approvalDefinitionsToBeDeleted.isEmpty()) {
          entityTypesToUpdatePrefs =
              approvalDefinitionsToBeDeleted.stream()
                  .map(
                      definitionDetails ->
                          definitionDetails
                              .getRecordType()
                              .getDisplayValue()
                              .replace(WorkflowConstants.SPACE, WorkflowConstants.UNDERSCORE))
                  .collect(Collectors.joining(WorkflowConstants.COMMA));
        }
      }
    }

  if (disableSalesSettings || !StringUtils.isBlank(entityTypesToUpdatePrefs)){
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(ownerId);
    AppConnectExecuteDuzzitRequest actionRequest =
        prepareExecuteDuzzitRequest(workerActionRequest, authDetails, entityTypesToUpdatePrefs,
            disableSalesSettings);

    return appconnectDuzzitRestExecutionHelper.executeAppConnectRequest(
        actionRequest, workerActionRequest, authDetails);
  }
  return ImmutableMap.of(
      new StringBuilder(activityId)
          .append(WorkflowConstants.UNDERSCORE)
          .append(RESPONSE.getName())
          .toString(),
      Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(
        MetricName.BULK_APPROVAL_PREFS_UPDATE, Type.EXTERNAL_TASK_METRIC, exception);
  }

  private MultiValueMap<String, String> prepareInputs(String status, String entityTypes,Boolean disableSalesSettings) {
    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
    body.add(RepositoryConstants.STATUS, status);
    body.add(WorkflowConstants.ENTITY_TYPE, entityTypes);
    body.add(WorkflowConstants.DISABLE_SALES_SETTINGS,String.valueOf(disableSalesSettings));
    return body;
  }

  private AppConnectExecuteDuzzitRequest prepareExecuteDuzzitRequest(
      WorkerActionRequest workerActionRequest, AuthDetails authDetails, String entityTypes,Boolean disableSalesSettings) {

    return AppConnectExecuteDuzzitRequest.builder()
        .endpoint(
            appconnectDuzzitRestExecutionHelper.prepareRequestEndpoint(
                DISABLE_SETTINGS, authDetails))
        .realmId(workerActionRequest.getInputVariables().get(INTUIT_REALMID))
        .instanceId(workerActionRequest.getProcessInstanceId())
        .providerAppId(appConnectConfig.getProviderAppId())
        .inputs(prepareInputs(Status.DISABLED.toString(), entityTypes,disableSalesSettings))
        .externalTaskId(workerActionRequest.getTaskId())
        .build();
  }
}
