package com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations;

import java.time.Duration;
import java.util.Map;

public interface CacheClientOperation {

    /**
     * Retrieves the hash object value which is a map corresponding to the cache key
     *
     * @param cacheKey
     * @return
     */
    Map<String, String> getMap(String cacheKey);

    /**
     * Clears the entire content, i.e. all key-values of the map corresponding to the cache key
     *
     * @param hashObject
     * @return
     */
    void clearMap(Map<String, String> hashObject);

    /**
     * Removes the specific field-value from the hash object
     *
     * @param hashObject
     * @return
     */
    void removeKey(Map<String, String> hashObject, String hashKey);

    /**
     * Insert the value (string) corresponding to a field of the hash object
     *
     * @param hashObject
     * @param hashFieldValue
     */
    void put(Map<String, String> hashObject, String hashField, String hashFieldValue);

    /**
     * Insert the value (string) corresponding to all fields of the hash object at once in bulk.
     *
     * @param hashObject
     *      * @param hashFieldValue
     */
    void putAll(Map<String, String> hashObject, Map<String, String> hashFieldValue);


    /**
     * Set a TTL to the cache key
     *
     * @param hashObject
     * @param d
     */
    void setTTL(Map<String, String> hashObject, Duration d);


    /**
     * Returns if the field is present in the hash object
     *
     * @param hashObject
     * @param hashField
     * @return
     */
    boolean containsKeyInMap(Map<String, String> hashObject,
                                 String hashField);

    /**
     * Retrieves the value corresponding to the hash object field passed
     * in the type that is required
     *
     * @param hashObject
     * @param hashField
     * @param hashFieldValueType
     * @param <T>
     * @return
     */
    <T> T getValueFromMap(Map<String, String> hashObject,
                                String hashField,
                                Class<T> hashFieldValueType);
}
