package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.experimentation.ExperienceDecisionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.experimentation.ExperienceDecisionConstants;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.identity.exptplatform.assignment.entities.DefaultEntityIdImpl;
import com.intuit.identity.exptplatform.assignment.entities.EntityID;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.DECISION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

/**
 * Experience Decision Handler
 * This class is responsible for handling the Experience Decision task.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ExperienceDecisionHandler extends WorkflowTaskHandler {
    private final IXPManager ixpManager;
    private final WASContextHandler contextHandler;
    private static final String IXP_DESCRIPTION = "IXPService";

    @Override
    public TaskHandlerName getName() {
        return TaskHandlerName.EXPERIENCE_DECISION_HANDLER;
    }

    /**
     * Executes action to get the decision details and fetch the assignment decision.
     *
     * @param inputRequest
     * @param <T>
     * @return
     */
    @Override
    @SuppressWarnings("all")
    protected <T> Map<String, Object> executeAction(T inputRequest) {
        WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
        Map<String, String> inputVariables = workerActionRequest.getInputVariables();
        Object assignmentDecision = getAssignmentDecision(inputVariables, workerActionRequest);
        return getResponseMap((WorkerActionRequest) inputRequest, assignmentDecision);
    }

    /**
     * Get Response Map with Decision and Response
     *
     * @param inputRequest
     * @param assignmentDecision
     * @param <T>
     * @return
     */
    @NotNull
    @SuppressWarnings("all")
    private static <T> Map<String, Object> getResponseMap(WorkerActionRequest inputRequest, Object assignmentDecision) {
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put(new StringBuilder(inputRequest.getActivityId())
                .append(UNDERSCORE)
                .append(DECISION.getName())
                .toString(), assignmentDecision);

        responseMap.put(new StringBuilder(inputRequest.getActivityId())
                        .append(UNDERSCORE)
                        .append(RESPONSE.getName())
                        .toString(),
                Boolean.TRUE);
        return responseMap;
    }

    /**
     * Gets Assignment Decision.
     * @param inputVariables
     * @param workerActionRequest
     * @return
     */
    @NotNull
    @SuppressWarnings("all")
    private Object getAssignmentDecision(Map<String, String> inputVariables, WorkerActionRequest workerActionRequest) {
        ExperienceDecisionDetails experienceDecisionDetails = getDecisionDetails(inputVariables);
        String ownerId = getOwnerId(workerActionRequest);
        Map<String, String> entityId = ObjectConverter.fromJson(experienceDecisionDetails.getEntityId(),
            new TypeReference<Map<String, String>>(){});
        Map<String, Object> contextMap = ObjectConverter.fromJson(experienceDecisionDetails.getContextMap(),
            new TypeReference<Map<String, Object>>(){});
        return getAssigment(experienceDecisionDetails, entityId, contextMap, ownerId);
    }


    /**
     * Get Decision Details from InputVariables. Mandated check on FeatureFlag and DefaultValue.
     * @param inputVariables
     * @return
     */
    @Nullable
    private static ExperienceDecisionDetails getDecisionDetails(Map<String, String> inputVariables) {
        String decisionDetailsJson = inputVariables.get(ExperienceDecisionConstants.DECISION_DETAILS);
        ExperienceDecisionDetails experienceDecisionDetails = ObjectConverter.fromJson(decisionDetailsJson, new TypeReference<ExperienceDecisionDetails>(){});
        WorkflowVerfiy.verify(ObjectUtils.isEmpty(experienceDecisionDetails)
                || StringUtils.isEmpty(experienceDecisionDetails.getDefaultValue())
                || StringUtils.isEmpty(experienceDecisionDetails.getFeatureFlag()), WorkflowError.DECISION_DETAILS_NOT_FOUND);
        return experienceDecisionDetails;
    }

    /**
     * Fetch OwnerId from ContextHandler and if not present use BusinessKey
     * @param workerActionRequest
     * @return
     */
    private String getOwnerId(WorkerActionRequest workerActionRequest) {
        String ownerId = contextHandler.get(WASContextEnums.OWNER_ID);
        if(StringUtils.isEmpty(ownerId)) {
            ownerId = workerActionRequest.getBusinessKey();
        }
        return ownerId;
    }

    @Override
    protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
        metricLogger.logErrorMetric(MetricName.EXPERIENCE_DECISION_EXTERNAL_TASK, Type.EXTERNAL_TASK_METRIC, exception);
    }

    /**
     * Call IXP Based on Varian Type. Default is String
     * @param experienceDecisionDetails
     * @param entityId
     * @param contextMap
     * @param ownerId
     * @return
     */
    private Object getAssigment(ExperienceDecisionDetails experienceDecisionDetails, Map<String, String> entityId,
                           Map<String, Object> contextMap, String ownerId) {
        if(experienceDecisionDetails.getVariationType().equals(ExperienceDecisionConstants.BOOLEAN)){
            return Objects.isNull(experienceDecisionDetails.getEntityId()) ?
                ixpManager.getBoolean(
                        experienceDecisionDetails.getFeatureFlag(), Boolean.parseBoolean(experienceDecisionDetails.getDefaultValue()),
                        contextMap, Long.valueOf(ownerId)) :
                ixpManager.getBoolean(
                        experienceDecisionDetails.getFeatureFlag(), experienceDecisionDetails.getDefaultValue(),
                        getIXPEntityID(entityId, ownerId), contextMap);
        }
        return (Objects.isNull(experienceDecisionDetails.getEntityId()) ?
            ixpManager.getString(
                    experienceDecisionDetails.getFeatureFlag(), experienceDecisionDetails.getDefaultValue(),
                    contextMap,  Long.valueOf(ownerId)) :
            ixpManager.getString(
                    experienceDecisionDetails.getFeatureFlag(), experienceDecisionDetails.getDefaultValue(),
                    getIXPEntityID(entityId, ownerId), contextMap));
    }

    /**
     * Get EntityID from entityId Map
     * @param entityId
     * @return
     */
    public EntityID getIXPEntityID(Map<String, String> entityId, String ownerId) {
        return DefaultEntityIdImpl
            .builder()
            .realmOrCompanyId(entityId.getOrDefault(ExperienceDecisionConstants.ENTITY_ID_REALM_ID, ownerId))
            .ns(entityId.getOrDefault(ExperienceDecisionConstants.ENTITY_ID_NS, IXP_DESCRIPTION))
            .accountId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_ACCOUNT_ID))
            .ivid(entityId.get(ExperienceDecisionConstants.ENTITY_ID_IVID))
            .hashedPhoneNumberId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_HASHED_PHONE_NUMBER_ID))
            .version(entityId.get(ExperienceDecisionConstants.ENTITY_ID_VERSION))
            .ixpIvid(entityId.get(ExperienceDecisionConstants.ENTITY_ID_IXP_IVID))
            .hashedTurbotaxDesktopId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_HASHED_TURBOTAX_DESKTOP_ID))
            .canId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_CAN_ID))
            .hashedEmailId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_HASHED_EMAIL_ID))
            .pseudonymId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_PSEUDONYM_ID))
            .authId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_AUTH_ID))
            .mobileDeviceId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_MOBILE_DEVICE_ID))
            .mobileAdvertisingId(entityId.get(ExperienceDecisionConstants.ENTITY_ID_MOBILE_ADVERTISING_ID))
            .build();
    }
}
