package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ASSIGNEE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CREATE_TASK;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.START_INDEX_VALUE;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStep.StepNext;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Contains the logic to handle the migration and rollback related to multi
 * condition definition
 */
@Component
@AllArgsConstructor
public class MultiStepDefinitionTransformer extends BaseDefinitionTransformer {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final MultiStepConfig multiStepConfig;
  private final TemplateDetailsRepository templateDetailsRepository;
  private final DefinitionDetailsRepository definitionDetailsRepository;

  @Override
  public boolean isMigrationCase(TransformationDecisionDTO transformationDecisionDTO) {
    TemplateDetails templateDetails = templateDetailsRepository.findByTemplateId(
        transformationDecisionDTO.getTemplateId());
    String templateName = transformationDecisionDTO.getDefinitionDetails().getTemplateDetails()
        .getTemplateName();
    return multiStepConfig.getWorkflowTemplates().get(templateName).getSingleStepVersion() <
        templateDetails.getVersion();
  }

  @Override
  public void migrate(Definition readDefinition, DefinitionDetails definitionDetails) {
    try {
      String recordType = definitionDetails.getRecordType().getRecordType();
      List<WorkflowStep> workflowSteps = readDefinition.getWorkflowSteps();

      List<ActionMapper> actionMappers = workflowSteps.stream().findFirst().get().getActions();
      workflowSteps.stream().findFirst().get().setActions(null);

      // building action group for multi condition
      ActionGroup actionGroup = new ActionGroup();
      String actionKey = actionMappers.stream().findFirst().get().getActionKey();
      actionGroup.setActionKey(actionKey);
      List<Action> subActions = actionMappers.stream().map(ActionMapper::getAction).collect(
          Collectors.toList());
      Action action = new Action();
      Record record = customWorkflowConfig.getRecordObjForType(recordType);
      //Ex. parentActionId = "sendForApproval"
      String parentActionId = MultiStepUtil.getTemplateDataForReadOneTemplate(record, actionKey);
      GlobalId actionId = GlobalId.builder()
          .setRealmId(Long.toString(definitionDetails.getOwnerId()))
          .setLocalId(parentActionId).build();
      action.setId(actionId);
      action.setSubActions(subActions);
      action.setSelected(Boolean.TRUE);
      List<InputParameter> parameters = subActions.stream()
          .filter(filterAction -> filterAction.getId().getLocalId().equals(CREATE_TASK))
          .findFirst().get().getParameters().stream()
          .filter(parameter -> parameter.getParameterName().equalsIgnoreCase(ASSIGNEE))
          .collect(Collectors.toList());
      action.setParameters(parameters);
      actionGroup.setAction(action);

      // creating action step
      WorkflowStep actionStep = new WorkflowStep();
      actionStep.setActionGroup(actionGroup);
      actionStep.setStepType(StepTypeEnum.ACTION);
      GlobalId actionStepId = GlobalId.builder()
          .setRealmId(Long.toString(definitionDetails.getOwnerId()))
          .setLocalId(definitionDetails.getDefinitionId()).build();
      actionStep.setId(actionStepId);
      workflowSteps.add(actionStep);

      // building condition step
      WorkflowStep conditionStep = workflowSteps.stream().findFirst().get();
      StepNext yesPath = new StepNext();
      yesPath.setWorkflowStepId(workflowSteps.get(1).getId().toString());
      yesPath.setLabel(NextLabelEnum.YES);
      conditionStep.setNext(START_INDEX_VALUE, yesPath);
      conditionStep.setStepType(StepTypeEnum.CONDITION);

    } catch (Exception migrateException) {
      WorkflowLogger.error(() -> WorkflowLoggerRequest.builder()
          .message("Exception=%s Payload=%s", migrateException, readDefinition));
    }
  }

  // In rollback, we will get definitionDetails of the definition which was last working correctly
  // and then we will fetch the definition details of the definition we need to rollback
  @Override
  public void rollback(Definition readDefinition, DefinitionDetails definitionDetails) {
    String definitionKey = definitionDetails.getDefinitionKey();
    Long ownerId = definitionDetails.getOwnerId();
    // fetching the definitionDetails of the latest definition with internal status null for the
    // given definition details to get the definition id of the definition we need to rollback
    DefinitionDetails latestDefinitionDetails = definitionDetailsRepository.findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
        ownerId, definitionKey);
    readDefinition.setId(GlobalId.builder().setRealmId(Long.toString(ownerId))
        .setLocalId(latestDefinitionDetails.getDefinitionId()).build());
  }

}
