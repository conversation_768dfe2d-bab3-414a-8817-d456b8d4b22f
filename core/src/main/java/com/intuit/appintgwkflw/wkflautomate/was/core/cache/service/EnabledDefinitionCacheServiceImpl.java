package com.intuit.appintgwkflw.wkflautomate.was.core.cache.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations.CacheClientOperation;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.CacheConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.schema.EnabledDefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CUSTOM_TEMPLATE_CATEGORY;


@Component
@RequiredArgsConstructor
@ConditionalOnProperty(value = "cache.enabled", havingValue = "true")
public class EnabledDefinitionCacheServiceImpl implements EnabledDefinitionCacheService {

    private final CacheClientOperation cacheClient;
    private final DefinitionDetailsRepository definitionDetailsRepository;
    private final CustomWorkflowQueryCapability customWorkflowQueryCapability;
    private final CacheConfiguration cacheConfiguration;

    @Value("${batch-job.stepConfig.cacheWarmup.workflows:#{null}}")
    private String enabledWorkflows;

    private Set<String> enabledWorkflowsSet = Collections.emptySet();
    private Duration ttlDuration;

    private static final String ENABLED_DEFINITION_CACHE = "ed";
    private static final String CACHE_DELIMITER = "::";

    /**
     * Split the string of comma separated enabled workflows into a set of workflows and populate
     */
    @PostConstruct
    private void init() {
        if(enabledWorkflows != null) {
            enabledWorkflowsSet = new HashSet<>(Arrays.asList(enabledWorkflows.split(",")));
        }
        if(cacheConfiguration.getTtlDurationMillis() != null) {
            ttlDuration = Duration.ofMillis(cacheConfiguration.getTtlDurationMillis());
        }
    }

    /**
     * For every definition (bpmn) passed, checks the realmId, record type and action key and
     * populates cache for each of these configurations by taking the latest values from the DB
     * so that cache gets updated values for the definitions newly created/enabled/disabled/etc
     *
     * @param definitionDetails
     */
    public void updateCacheWithDefinitionDetails(DefinitionDetails definitionDetails) {

        if(definitionDetails == null) {
            return;
        }

        // Return if not custom workflow. Currently, caching only custom workflow definitions
        if(!customWorkflowQueryCapability.isCustomWorkflow(
                definitionDetails.getTemplateDetails().getTemplateName(),
                definitionDetails.getRecordType())) {
            return;
        }

        // If there are workflow filters, cache only for definitions of those workflows
        if(CollectionUtils.isNotEmpty(enabledWorkflowsSet) &&
            !enabledWorkflowsSet.contains(definitionDetails.getTemplateDetails().getTemplateName())) {
            WorkflowLogger.logDebug("Skipping cache update for definitionId=%s ", definitionDetails.getDefinitionId());
            return;
        }

        // TODO: If call is update, then do this only if status change, else migration will flood cache
        populateEnabledDefinitionKeysForRealmIdRecordTypeAndAction(
                definitionDetails.getOwnerId(),
                definitionDetails.getRecordType(),
                getActionFromDefinitionDetail(definitionDetails)
        );
    }

    @Override
    public void populateEnabledDefinitionKeysForRealmId(Long realmId) {
        // retrieve all enabled definitions for realm id
        List<DefinitionDetails> definitionDetails = getAllEnabledDefinitionsForRealm(
                realmId
        );

        // Create map of record type to - map of action to enabled definition keys
        Map<RecordType, Map<String, EnabledDefinitionDetails>> recordToDefinitionKeys = new HashMap<>();
        definitionDetails
                .forEach((dd) -> {
                        String action = getActionFromDefinitionDetail(dd);
                        recordToDefinitionKeys.putIfAbsent(dd.getRecordType(), new HashMap<>());
                        if(!recordToDefinitionKeys.get(dd.getRecordType()).containsKey(action)){
                            EnabledDefinitionDetails ed = new EnabledDefinitionDetails();
                            ed.setDefinitionKeys(new HashSet<>());
                            recordToDefinitionKeys.get(dd.getRecordType()).putIfAbsent(action, ed);
                        }
                        recordToDefinitionKeys.get(dd.getRecordType())
                                .get(action)
                                .getDefinitionKeys()
                                .add(dd.getDefinitionKey());
                    }
                );

        // For each record type, create map of action to EnabledDefinition entity for that action
        Map<RecordType, Map<String, String>> recordToActionToDefinitionKeysSerialized = new HashMap<>();
        recordToDefinitionKeys.forEach(
            (recordType, actionToEnabledDefinitionDetails) -> {
                actionToEnabledDefinitionDetails
                    .forEach(
                        (actionKey, enabledDefinitionDetails) -> {
                            recordToActionToDefinitionKeysSerialized.putIfAbsent(recordType, new HashMap<>());
                            recordToActionToDefinitionKeysSerialized
                                .get(recordType)
                                .put(
                                    actionKey,
                                    enabledDefinitionDetails.toString()
                                );
                        }
                    );
            }
        );

        // bulk insert into cache for all actions
        // do so for each realmid-recordtype combination
        recordToActionToDefinitionKeysSerialized
                .forEach((recordType, actionToDefinitionKeysMap) -> {
                    String cacheKey = constructCacheKey(realmId, recordType);
                    Map<String, String> realmRecordTypeMap = cacheClient.getMap(cacheKey);
                    realmRecordTypeMap.keySet().forEach((cacheActionKey) -> {
                        if (!actionToDefinitionKeysMap.containsKey(cacheActionKey)) {
                            cacheClient.removeKey(realmRecordTypeMap, cacheActionKey);
                        }
                    });
                    cacheClient.putAll(
                            realmRecordTypeMap,
                            actionToDefinitionKeysMap
                    );
                    if(ttlDuration!=null) cacheClient.setTTL(realmRecordTypeMap, ttlDuration);
                });
    }

    /**
     * Fetches list of all enabled definition details for the realmId, action and recordType of the definition passed as argument,
     * and re-populates cache with the latest values from the database so that there is no inconsistency between cache and DB
     *
     */
    private void populateEnabledDefinitionKeysForRealmIdRecordTypeAndAction(
            Long realmId,
            RecordType recordType,
            String actionKey
    ) {

        // TODO: Operation to Redis are atomic (lock to that record). Just recheck.

        String cacheKey = constructCacheKey(
                realmId,
                recordType
        );


        EnabledDefinitionDetails enabledDefinitionDetails = retrieveAllEnabledDefinitionsForOwnerIdRecordTypeAndAction(
                realmId,
                recordType,
                actionKey
        );

        // returns empty map if no key found
        Map<String, String> realmIdMap = cacheClient.getMap(cacheKey);

        // insert just definitionKeys for the action in cache
        if(insertIntoCache(realmIdMap, enabledDefinitionDetails, actionKey)) {
            cacheClient.put(
                    realmIdMap,
                    actionKey,
                    // considers the definition being created too
                    enabledDefinitionDetails.toString()
            );
            if(ttlDuration!=null) cacheClient.setTTL(realmIdMap, ttlDuration);
        }
    }


    /**
     * Returns whether the enabled definitions are to be populated into the cache
     *
     *  Scenarios:
     *     * If no enabled definitions for the key (realmId:recordType) in DB and the key doesn't even exist in the cache,
     *         don't need to create the key and store an empty value against it
     *     * If key exists in cache with non-empty value for the actionKey, but there are no enabled
     *         definitions in DB for that key-action combination, then update key with empty value
     *     * If there are enabled definitions for the key in the DB, update this in cache, whether
     *         that key is already present or not in the cache
     *
     *  Thus, insert if the key has been found in cache with non-empty value for the action or there are enabled definitions for the key-action combination
     */
    private boolean insertIntoCache(Map<String, String> realmIdMap,
                                    EnabledDefinitionDetails enabledDefinitionDetails,
                                    String actionKey) {
        return isEnabledDefinitionsPresentForActionInCache(realmIdMap, actionKey) ||
                !enabledDefinitionDetails.getDefinitionKeys().isEmpty();
    }

    /**
     * Returns true if there already exists a list of enabled definition
     * keys for the realmId:recordType and actionkey Field in the cache
     *
     * @param realmIdMap
     * @param actionKey
     * @return
     */
    private boolean isEnabledDefinitionsPresentForActionInCache(Map<String, String> realmIdMap, String actionKey) {
        EnabledDefinitionDetails ed = cacheClient.getValueFromMap(
                realmIdMap,
                actionKey,
                EnabledDefinitionDetails.class);

        return ed!=null && !ed.getDefinitionKeys().isEmpty();
    }


    /**
     * Fetches the list of definitionKeys of
     * "EnabledDefinitionDetails" for the realmid and action (e.g. 'reminder')
     * to insert into cache
     * @return
     */
    private EnabledDefinitionDetails retrieveAllEnabledDefinitionsForOwnerIdRecordTypeAndAction(
            Long realmId,
            RecordType recordType,
            String actionKey
    ) {
        EnabledDefinitionDetails ed = new EnabledDefinitionDetails();
        ed.setDefinitionKeys(getAllEnabledDefinitionKeysForRealmForWorkflow(
                realmId,
                recordType,
                actionKey
        ));
        return ed;
    }

    /**
     * Get list of all "custom" enabled definitions for the realm
     * @param realmId
     * @return
     */
    private List<DefinitionDetails> getAllEnabledDefinitionsForRealm(
            Long realmId
    ) {
        return definitionDetailsRepository
                .findAllEnabledDefinitionsForOwnerIdTemplateCategoryAndWorkflows(
                        realmId,
                        ModelType.BPMN,
                        CUSTOM_TEMPLATE_CATEGORY,
                        false,
                        enabledWorkflowsSet
                ).orElse(Collections.emptyList());
    }

    /**
     * Fetch just the definitionKeys of all enabled definitions for the realmId, recordType and template
     * The template name corresponding to the custom workflow is found using the actionKey
     *
     * @param realmId
     * @param recordType
     * @param actionKey
     * @return
     */
    private Set<String> getAllEnabledDefinitionKeysForRealmForWorkflow(
            Long realmId,
            RecordType recordType,
            String actionKey
    ) {
        String templateName = CustomWorkflowType.getTemplateName(actionKey);
        Set<String> enabledDefinitionsList = definitionDetailsRepository
                .findDefinitionKeysOfAllEnabledDefinitionsForOwnerIdModelRecordTypeAndTemplateName(
                        realmId,
                        ModelType.BPMN,
                        recordType,
                        templateName
                )
                .orElseGet(() -> {
                    WorkflowLogger.logWarn("Could not find any enabled definitions for realmId=%s, recordType=%s and templateName=%s ", realmId, recordType, templateName);
                    return Collections.emptySet();
                });

        return enabledDefinitionsList;
    }




    /**
     * Fetch action key for the workflow
     *
     * @param definitionDetails
     * @return
     */
    private String getActionFromDefinitionDetail(DefinitionDetails definitionDetails) {
        return CustomWorkflowType.getActionKey(
                definitionDetails.getTemplateDetails().getTemplateName()
        );
    }

    /**
     * Constructs the cache key for enabled definitions schema
     * Format:- ed::realmId::recordType
     *
     * @param realmId
     * @param recordType
     * @return
     */
    private String constructCacheKey(
            Long realmId,
            RecordType recordType
    ) {
        return ENABLED_DEFINITION_CACHE
                .concat(CACHE_DELIMITER)
                .concat(realmId.toString())
                .concat(CACHE_DELIMITER)
                .concat(recordType.toString().toLowerCase()); // convert record type to lower case for consistency
    }

}
