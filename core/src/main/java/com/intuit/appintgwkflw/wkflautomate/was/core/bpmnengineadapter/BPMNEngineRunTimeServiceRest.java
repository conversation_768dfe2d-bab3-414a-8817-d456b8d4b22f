package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.StartProcessRequest;
import java.util.List;
import java.util.Map;

public interface BPMNEngineRunTimeServiceRest {

  /**
   * @param startProcessRequest: Contains BPMN definition ID, variable map and response type to
   *     start process
   * @return Response: Starts the process and returns the API response
   */
  Map<String, Object> startProcess(StartProcessRequest startProcessRequest);

  /**
   * @param evaluateRuleRequest: Contains Definition ID, variable map, headers and response type
   * @return EvaluateRuleResponse: Evaluates the DMN and gives back the response with result
   */
  List<Map<String, Object>> evaluateDecision(EvaluateRuleRequest evaluateRuleRequest);

  /**
   * @param correlateMessage contains process instance id, message name variables and headers
   * @return true if successfully signalled
   */
  boolean correlateMessage(CorrelateMessage correlateMessage);

  /**
   * @param correlateAllMessage contains businessKey, message name and correlation Keys
   * @return correlate message response
   */
  boolean correlateAllMessage(CorrelateAllMessage correlateAllMessage);

  /**
   * @param correlateAllMessage contains businessKey, message name and correlation Keys
   * @return correlate message response
   */
  boolean correlateAllMessageAsync(CorrelateAllMessageAsync correlateAllMessage);

  /**
   * Used for events with no retries configured but throws retriable exception
   *
   * @param correlateMessage
   * @return true if successfully signalled
   */
  boolean correlateMessageEvent(CorrelateMessage correlateMessage);
}
