package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.core.ticket.OfflineTicketService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/ticket")
@AllArgsConstructor

/**
 * Controller to for all Access ticket related API's
 *
 * <AUTHOR>
 */
public class OfflineTicketController {

  private final OfflineTicketService offlineTicketService;

  @Metric(name = MetricName.UPDATE_OFFLINE_TICKET, type = Type.API_METRIC)
  @PostMapping(value = "/update")
  public WorkflowGenericResponse updateOfflineTicket() {
    return offlineTicketService.updateOfflineTicket();
  }
}
