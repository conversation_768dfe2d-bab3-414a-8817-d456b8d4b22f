package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class ActivityConstants {

  public final String EXTENSION_PROPERTY_ACTIVITY_TYPE = "type";
  public final String EXTENSION_PROPERTY_ACTIVITY_NAME = "activityName";
  public final String TASK_STATUS_OPEN = "open";
  public final String TASK_STATUS_CREATED = "created";
  public final String TASK_STATUS_FAILED = "failed";
  public final String TASK_STATUS_COMPLETE = "completed";
  public final String TASK_STATUS_SUCCESS = "success";
  public final String TASK_EVENT_TYPE_UPDATE = "update";
  public final String MANDATORY_EXTERNAL_REFERENCES = "wasReferences";
  public final String EXTERNAL_TASK_ID = "externalTaskId";
  public final String DUE_DATE_FORMAT = "yyyy-MM-dd";
  public final String NO_ACTION = "NO_ACTION";
  public final String WORKFLOW_DEFAULT_TRANSACTION_VARIABLE = "txnId";
  public final String EXTENSION_PROPERTY_TRANSACTION_VARIABLE = "txnVariable";
  public final String EXTENSION_PROPERTY_TRANSACTION_MODE = "txnMode";
  public final String ACTIVITY_RUNTIME_ATTRIBUTES = "runtimeAttributes";
  public final String ADDITIONAL_TASK_ATTRIBUTES = "additionalTaskAttributes";
  public final String SCOPE_ACTIVITY = "activity";
  public final String VARIABLE_MAP_VALUE = "value";
  public final String VARIABLE_MAP_TYPE = "type";
  public static final String ACTIVITY_DOMAIN = "domain";
  public static final String ACTIVITY_USECASE = "usecase";
  public static final String ACTIVITY_PROJECT = "project";
  public static final String TASK_TYPE = "type";
  public static final String TARGET_DOMAIN = "target_domain";
  public static final String TARGET_APP = "target_app";
  public static final String TARGET_USECASE = "target_usecase";
  public static final String TARGET_REALM = "target_realm";

  public static final String INTUIT_HEADER = "intuit_";
  public static final String ACTIVITY_DETAILS_TABLE_ATTRIBUTES_KEY_MODEL_ATTRIBUTES = "modelAttributes";
  public static final String ORIGINATING_ASSETALIAS = "originating_assetalias";
  public static final String ORIGINATING_ASSET_ALIAS = "originating_asset_alias";
  
  public static final String ACTIVITY_CONSTANTS_TASK_ID = "id";
  public static final String ACTIVITY_CONSTANTS_TASK_ACTIVITY_ID = "activityId";

}