package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ConfigTemplate extends Record {
  // Name of The Pre-Canned Template
  private String name;
  // Record Type name
  private String record;
  // Description
  private String description;
  // specifies Template Category
  private final String category = TemplateCategory.CUSTOM.name();

  private RecurrenceRuleMapper recurrenceRule;

  private List<NameValue> labels;
}
