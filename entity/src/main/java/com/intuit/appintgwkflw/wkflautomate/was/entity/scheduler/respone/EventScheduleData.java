package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EventScheduleData {
  private String id;
  private ScheduleStatus status;
}
